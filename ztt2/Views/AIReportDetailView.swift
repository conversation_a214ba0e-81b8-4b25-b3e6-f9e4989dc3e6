//
//  AIReportDetailView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import SwiftUI

/**
 * AI报告详情页面
 * 参考ztt1项目ReportContentView设计，实现全屏沉浸式的报告展示体验
 */
struct AIReportDetailView: View {

    // MARK: - Properties

    let report: AIAnalysisReport
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    @State private var contentAppeared = false
    @State private var showingToast = false
    @State private var toastMessage = ""

    // MARK: - Body

    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 顶部占位，为导航栏留空间
                Color.clear
                    .frame(height: 80)

                VStack(spacing: 24) {
                    // 报告头部信息
                    reportHeaderSection

                    // 报告主要内容
                    reportMainContent

                    // 操作按钮
                    actionButtons

                    // 底部间距
                    Color.clear
                        .frame(height: 40)
                }
                .padding(.horizontal, 20)
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            withAnimation(.easeInOut(duration: 0.8).delay(0.2)) {
                contentAppeared = true
            }
        }
        .overlay(
            // 顶部导航栏
            VStack {
                topNavigationBar
                Spacer()
            }
        )
        .overlay(
            // Toast提示
            VStack {
                Spacer()
                if showingToast {
                    ToastView(message: toastMessage)
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: showingToast)
                }
            }
            .padding(.bottom, 50)
        )
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
    }
    
    // MARK: - View Components

    /**
     * 顶部导航栏
     */
    private var topNavigationBar: some View {
        HStack {
            Button(action: {
                dismiss()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .medium))
                    Text("返回")
                        .font(.system(size: 16))
                }
                .foregroundColor(DesignSystem.Colors.textPrimary)
            }

            Spacer()

            Text("AI分析")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Spacer()

            Button(action: {
                showingShareSheet = true
            }) {
                Image(systemName: "square.and.arrow.up")
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 8)
        .background(Color.clear)
    }

    /**
     * 报告头部信息
     */
    private var reportHeaderSection: some View {
        VStack(spacing: 16) {
            // 报告标题
            HStack {
                Image(systemName: reportTypeIcon)
                    .font(.system(size: 24))
                    .foregroundColor(Color(hex: "#74c07f"))

                Text(report.reportType.displayName)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()
            }

            // 基本统计信息
            statisticsInfoCard
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : 20)
        .animation(.easeOut(duration: 0.6).delay(0.1), value: contentAppeared)
    }

    /**
     * 统计信息卡片
     */
    private var statisticsInfoCard: some View {
        VStack(spacing: 12) {
            // 成员基本信息
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("分析对象")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text(report.memberName)
                        .font(.system(size: 16))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }

                Spacer()

                // 生成时间
                VStack(alignment: .trailing, spacing: 4) {
                    Text("生成时间")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textSecondary)

                    Text(DateFormatter.shortDateTime.string(from: report.createdAt))
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
            }

            Divider()
                .background(Color(hex: "#E8F5E8"))

            // 成员详细信息
            HStack {
                Text("成员信息")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(DesignSystem.Colors.textSecondary)

                Spacer()

                Text("\(report.memberRole) · \(report.memberAge)岁")
                    .font(.system(size: 14))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
        }
        .padding(20)
        .background(Color(hex: "#F8FDF0"))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color(hex: "#E8F5E8"), lineWidth: 1)
        )
    }

    /**
     * 报告主要内容
     */
    private var reportMainContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 分析报告标题
            HStack {
                Image(systemName: "doc.text")
                    .font(.system(size: 18))
                    .foregroundColor(Color(hex: "#74c07f"))

                Text("分析内容")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Spacer()
            }

            // AI分析内容
            analysisContentView
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : 30)
        .animation(.easeOut(duration: 0.8).delay(0.3), value: contentAppeared)
    }

    /**
     * 分析内容视图
     */
    private var analysisContentView: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 参考ztt1的样式，直接使用MarkdownText，不添加额外样式
            MarkdownText(content: report.content)
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color(hex: "#E8F5E8"), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
    }

    /**
     * 操作按钮区域
     */
    private var actionButtons: some View {
        VStack(spacing: 16) {
            // 复制报告按钮
            copyButton

            // 添加提示文本
            Text("长按可选择文本进行复制")
                .font(.system(size: 12))
                .foregroundColor(DesignSystem.Colors.textTertiary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 10)
                .padding(.top, 4)

            // AI生成内容仅供参考提示
            Text("AI生成内容仅供参考")
                .font(.system(size: 11))
                .foregroundColor(DesignSystem.Colors.textTertiary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 10)
                .padding(.top, 2)
        }
        .opacity(contentAppeared ? 1.0 : 0.0)
        .offset(y: contentAppeared ? 0 : 20)
        .animation(.easeOut(duration: 0.6).delay(0.5), value: contentAppeared)
    }

    private var copyButton: some View {
        Button(action: {
            copyReportToClipboard()
        }) {
            HStack(spacing: 8) {
                Image(systemName: "doc.on.doc")
                    .font(.system(size: 14))
                    .foregroundColor(.white)

                Text("复制报告")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(hex: "#74c07f"),
                        Color(hex: "#5da961")
                    ]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(16)
            .shadow(color: Color(hex: "#74c07f").opacity(0.3), radius: 5, x: 0, y: 3)
        }
        .scaleEffect(contentAppeared ? 1.0 : 0.8)
        .animation(.spring(response: 0.6, dampingFraction: 0.8, blendDuration: 0), value: contentAppeared)
        .buttonStyle(PressedButtonStyle())
    }

    // MARK: - Computed Properties
    
    /// 报告类型图标
    private var reportTypeIcon: String {
        switch report.reportType {
        case .behaviorAnalysis:
            return "chart.line.uptrend.xyaxis"
        case .growthReport:
            return "heart.text.square"
        }
    }
    
    /// 报告描述
    private var reportDescription: String {
        switch report.reportType {
        case .behaviorAnalysis:
            return "基于积分记录数据，运用行为分析学理论生成的专业分析报告"
        case .growthReport:
            return "基于成长日记内容，运用儿童心理学理论生成的专业成长报告"
        }
    }
    
    // MARK: - Helper Methods

    /**
     * 复制报告到剪贴板
     */
    private func copyReportToClipboard() {
        let shareText = generateShareText()
        UIPasteboard.general.string = shareText

        // 触觉反馈
        let feedbackGenerator = UINotificationFeedbackGenerator()
        feedbackGenerator.notificationOccurred(.success)

        // 显示Toast提示
        showToast("报告已复制到剪贴板")
        print("📋 报告已复制到剪贴板")
    }

    /**
     * 显示Toast提示
     */
    private func showToast(_ message: String) {
        toastMessage = message
        withAnimation {
            showingToast = true
        }

        // 2秒后自动隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation {
                showingToast = false
            }
        }
    }

    /// 生成分享文本
    private func generateShareText() -> String {
        let header = """
        【\(report.reportType.displayName)】
        分析对象：\(report.memberName)（\(report.memberRole)，\(report.memberAge)岁）
        生成时间：\(DateFormatter.fullDateTime.string(from: report.createdAt))

        """

        let content = report.content

        let footer = """


        ——————————————————
        由转团团AI分析功能生成
        """

        return header + content + footer
    }
}

// MARK: - CoreData版本的AIReportDetailView

/**
 * 基于CoreData AIReport实体的报告详情页面
 */
struct AIReportDetailCoreDataView: View {
    
    let aiReport: AIReport
    @Environment(\.dismiss) private var dismiss
    @State private var showingShareSheet = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 报告头部信息
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Label(aiReport.reportTypeDisplayName, systemImage: reportTypeIcon)
                                .font(.headline)
                                .foregroundColor(.blue)
                            
                            Spacer()
                            
                            Text(aiReport.formattedCreatedAt)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        if let member = aiReport.member {
                            HStack(spacing: 12) {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("分析对象")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    
                                    Text("\(member.displayName) · \(member.roleDisplayName) · \(member.age)岁")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                }
                                
                                Spacer()
                            }
                        }
                        
                        if let summary = aiReport.inputDataSummary {
                            Divider()
                            
                            Text(summary)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    
                    // 报告内容
                    VStack(alignment: .leading, spacing: 16) {
                        Text("分析内容")
                            .font(.headline)
                        
                        MarkdownText(content: aiReport.content ?? "")
                            .font(.body)
                            .lineSpacing(4)
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(12)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                }
                .padding()
            }
            .navigationTitle("AI分析报告")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingShareSheet = true
                    } label: {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            }
        }
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(items: [generateShareText()])
        }
    }
    
    private var reportTypeIcon: String {
        switch aiReport.reportType ?? "analysis" {
        case "analysis":
            return "chart.line.uptrend.xyaxis"
        case "growth":
            return "heart.text.square"
        default:
            return "doc.text"
        }
    }
    
    private func generateShareText() -> String {
        let header = """
        【\(aiReport.reportTypeDisplayName)】
        分析对象：\(aiReport.member?.displayName ?? "未知")
        生成时间：\(aiReport.formattedCreatedAt)
        
        """
        
        let content = aiReport.content ?? ""
        
        let footer = """
        
        
        ——————————————————
        由转团团AI分析功能生成
        """
        
        return header + content + footer
    }
}

// MARK: - Supporting Views

/**
 * 增强的Markdown文本渲染器
 * 支持iOS 15.6+的markdown格式渲染
 */
struct MarkdownText: View {
    let content: String

    init(content: String) {
        self.content = content
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ForEach(parseMarkdownContent(), id: \.id) { element in
                renderMarkdownElement(element)
            }
        }
        .textSelection(.enabled)
    }

    // MARK: - Markdown解析

    /**
     * 解析markdown内容为结构化元素
     */
    private func parseMarkdownContent() -> [MarkdownElement] {
        let lines = content.components(separatedBy: .newlines)
        var elements: [MarkdownElement] = []
        var currentParagraph: [String] = []

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)

            if trimmedLine.isEmpty {
                // 空行，结束当前段落
                if !currentParagraph.isEmpty {
                    elements.append(MarkdownElement(
                        id: UUID(),
                        type: .paragraph,
                        content: currentParagraph.joined(separator: " ")
                    ))
                    currentParagraph.removeAll()
                }
            } else if trimmedLine.hasPrefix("# ") {
                // 一级标题
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .heading1,
                    content: String(trimmedLine.dropFirst(2))
                ))
            } else if trimmedLine.hasPrefix("## ") {
                // 二级标题
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .heading2,
                    content: String(trimmedLine.dropFirst(3))
                ))
            } else if trimmedLine.hasPrefix("### ") {
                // 三级标题
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .heading3,
                    content: String(trimmedLine.dropFirst(4))
                ))
            } else if trimmedLine.hasPrefix("- ") || trimmedLine.hasPrefix("* ") {
                // 列表项
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .listItem,
                    content: String(trimmedLine.dropFirst(2))
                ))
            } else {
                // 普通文本，添加到当前段落
                currentParagraph.append(trimmedLine)
            }
        }

        // 处理最后的段落
        finalizeParagraph(&currentParagraph, &elements)

        return elements
    }

    /**
     * 完成当前段落的处理
     */
    private func finalizeParagraph(_ currentParagraph: inout [String], _ elements: inout [MarkdownElement]) {
        if !currentParagraph.isEmpty {
            elements.append(MarkdownElement(
                id: UUID(),
                type: .paragraph,
                content: currentParagraph.joined(separator: " ")
            ))
            currentParagraph.removeAll()
        }
    }

    // MARK: - 渲染方法

    /**
     * 渲染markdown元素
     * 参考ztt1的样式设计，优化字体、间距和颜色
     */
    @ViewBuilder
    private func renderMarkdownElement(_ element: MarkdownElement) -> some View {
        switch element.type {
        case .heading1:
            Text(element.content)
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .padding(.vertical, 6)
                .frame(maxWidth: .infinity, alignment: .leading)
        case .heading2:
            Text(element.content)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .padding(.vertical, 5)
                .frame(maxWidth: .infinity, alignment: .leading)
        case .heading3:
            Text(element.content)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .padding(.vertical, 4)
                .frame(maxWidth: .infinity, alignment: .leading)
        case .paragraph:
            Text(parseInlineMarkdown(element.content))
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineSpacing(6)
                .fixedSize(horizontal: false, vertical: true)
                .padding(.vertical, 2)
                .frame(maxWidth: .infinity, alignment: .leading)
        case .listItem:
            HStack(alignment: .top, spacing: 12) {
                Text("•")
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .padding(.top, 2)
                Text(parseInlineMarkdown(element.content))
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .lineSpacing(6)
                    .fixedSize(horizontal: false, vertical: true)
                Spacer()
            }
            .padding(.leading, 16)
            .padding(.vertical, 2)
        }
    }

    /**
     * 解析行内markdown格式（粗体、斜体等）
     * 参考ztt1的处理方式，优化粗体文本渲染
     */
    private func parseInlineMarkdown(_ text: String) -> AttributedString {
        var attributedString = AttributedString(text)

        // 处理粗体 **text**
        let boldPattern = #"\*\*(.*?)\*\*"#
        if let regex = try? NSRegularExpression(pattern: boldPattern) {
            let matches = regex.matches(in: text, range: NSRange(text.startIndex..., in: text))
            for match in matches.reversed() {
                if let range = Range(match.range, in: text) {
                    let boldText = String(text[range])
                    let content = String(boldText.dropFirst(2).dropLast(2))
                    if let attributedRange = Range(match.range, in: attributedString) {
                        attributedString.replaceSubrange(attributedRange, with: AttributedString(content))
                        if let newRange = attributedString.range(of: content) {
                            // 使用系统字体的粗体版本，保持与ztt1一致的样式
                            attributedString[newRange].font = .system(size: 16, weight: .semibold)
                        }
                    }
                }
            }
        }

        return attributedString
    }
}

// MARK: - Markdown数据模型

/**
 * Markdown元素类型
 */
enum MarkdownElementType {
    case heading1
    case heading2
    case heading3
    case paragraph
    case listItem
}

/**
 * Markdown元素
 */
struct MarkdownElement {
    let id: UUID
    let type: MarkdownElementType
    let content: String
}

/**
 * Toast提示视图
 */
struct ToastView: View {
    let message: String

    var body: some View {
        Text(message)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.black.opacity(0.8))
            )
            .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
    }
}

/**
 * 分享功能
 */
struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: items, applicationActivities: nil)
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - Button Styles

/**
 * 按钮按压效果样式
 */
struct PressedButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - DateFormatter Extension

extension DateFormatter {
    static let fullDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()

    static let shortDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }()
}
