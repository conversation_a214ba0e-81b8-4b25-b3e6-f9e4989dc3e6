//
//  AIReportDetailViewTests.swift
//  ztt2Tests
//
//  Created by Augment Agent on 2025/8/1.
//

import XCTest
import SwiftUI
@testable import ztt2

/**
 * AI报告详情页面测试
 * 验证重构后的页面功能和视觉效果
 */
class AIReportDetailViewTests: XCTestCase {
    
    var testReport: AIAnalysisReport!
    
    override func setUp() {
        super.setUp()
        
        // 创建测试报告数据
        testReport = AIAnalysisReport(
            id: UUID(),
            reportType: .behaviorAnalysis,
            memberName: "测试成员",
            memberRole: "儿子",
            memberAge: 8,
            content: """
            # 8岁男孩成长分析报告
            
            ## 情绪变化分析
            
            根据积分记录分析，该成员在以下方面表现良好：
            
            - **积极情绪触发点**：学业成就感
            - **情绪调节能力**：逐步提升
            - **社交互动**：表现活跃
            
            ### 建议措施
            
            1. 继续鼓励学习积极性
            2. 培养情绪管理技能
            3. 增加社交活动机会
            
            这是一个包含markdown格式的测试报告内容。
            """,
            createdAt: Date()
        )
    }
    
    override func tearDown() {
        testReport = nil
        super.tearDown()
    }
    
    // MARK: - 基础功能测试
    
    /**
     * 测试页面基本渲染
     */
    func testBasicRendering() {
        let view = AIReportDetailView(report: testReport)
        
        // 验证视图可以正常创建
        XCTAssertNotNil(view)
        
        // 验证报告数据正确传递
        XCTAssertEqual(view.report.memberName, "测试成员")
        XCTAssertEqual(view.report.memberRole, "儿子")
        XCTAssertEqual(view.report.memberAge, 8)
        XCTAssertEqual(view.report.reportType, .behaviorAnalysis)
    }
    
    /**
     * 测试Markdown内容解析
     */
    func testMarkdownParsing() {
        let markdownText = MarkdownText(content: testReport.content)
        let elements = markdownText.parseMarkdownContent()
        
        // 验证解析出的元素数量和类型
        XCTAssertGreaterThan(elements.count, 0, "应该解析出markdown元素")
        
        // 验证包含标题元素
        let hasHeading1 = elements.contains { $0.type == .heading1 }
        let hasHeading2 = elements.contains { $0.type == .heading2 }
        let hasHeading3 = elements.contains { $0.type == .heading3 }
        let hasListItem = elements.contains { $0.type == .listItem }
        let hasParagraph = elements.contains { $0.type == .paragraph }
        
        XCTAssertTrue(hasHeading1, "应该包含一级标题")
        XCTAssertTrue(hasHeading2, "应该包含二级标题")
        XCTAssertTrue(hasHeading3, "应该包含三级标题")
        XCTAssertTrue(hasListItem, "应该包含列表项")
        XCTAssertTrue(hasParagraph, "应该包含段落")
    }
    
    /**
     * 测试分享文本生成
     */
    func testShareTextGeneration() {
        let view = AIReportDetailView(report: testReport)
        let shareText = view.generateShareText()
        
        // 验证分享文本包含必要信息
        XCTAssertTrue(shareText.contains("行为分析报告"), "应该包含报告类型")
        XCTAssertTrue(shareText.contains("测试成员"), "应该包含成员姓名")
        XCTAssertTrue(shareText.contains("儿子"), "应该包含成员角色")
        XCTAssertTrue(shareText.contains("8岁"), "应该包含成员年龄")
        XCTAssertTrue(shareText.contains("转团团AI分析功能生成"), "应该包含生成标识")
        XCTAssertTrue(shareText.contains(testReport.content), "应该包含报告内容")
    }
    
    // MARK: - 视觉效果测试
    
    /**
     * 测试报告类型图标
     */
    func testReportTypeIcon() {
        let behaviorReport = AIAnalysisReport(
            id: UUID(),
            reportType: .behaviorAnalysis,
            memberName: "测试",
            memberRole: "测试",
            memberAge: 8,
            content: "测试内容",
            createdAt: Date()
        )
        
        let growthReport = AIAnalysisReport(
            id: UUID(),
            reportType: .growthReport,
            memberName: "测试",
            memberRole: "测试",
            memberAge: 8,
            content: "测试内容",
            createdAt: Date()
        )
        
        let behaviorView = AIReportDetailView(report: behaviorReport)
        let growthView = AIReportDetailView(report: growthReport)
        
        // 验证不同报告类型使用不同图标
        XCTAssertEqual(behaviorView.reportTypeIcon, "chart.line.uptrend.xyaxis")
        XCTAssertEqual(growthView.reportTypeIcon, "heart.text.square")
    }
    
    /**
     * 测试报告描述
     */
    func testReportDescription() {
        let behaviorView = AIReportDetailView(report: testReport)
        let description = behaviorView.reportDescription
        
        XCTAssertTrue(description.contains("行为分析学理论"), "行为分析报告应该包含相关描述")
    }
    
    // MARK: - 性能测试
    
    /**
     * 测试大量内容的渲染性能
     */
    func testLargeContentPerformance() {
        // 创建大量内容的测试报告
        let largeContent = String(repeating: "这是一段测试内容。", count: 1000)
        let largeReport = AIAnalysisReport(
            id: UUID(),
            reportType: .behaviorAnalysis,
            memberName: "测试成员",
            memberRole: "儿子",
            memberAge: 8,
            content: largeContent,
            createdAt: Date()
        )
        
        // 测试渲染性能
        measure {
            let view = AIReportDetailView(report: largeReport)
            let markdownText = MarkdownText(content: largeReport.content)
            _ = markdownText.parseMarkdownContent()
        }
    }
}

// MARK: - 测试扩展

extension AIReportDetailView {
    
    /**
     * 暴露内部方法用于测试
     */
    func generateShareText() -> String {
        let header = """
        【\(report.reportType.displayName)】
        分析对象：\(report.memberName)（\(report.memberRole)，\(report.memberAge)岁）
        生成时间：\(DateFormatter.fullDateTime.string(from: report.createdAt))
        
        """
        
        let content = report.content
        
        let footer = """
        
        
        ——————————————————
        由转团团AI分析功能生成
        """
        
        return header + content + footer
    }
    
    var reportTypeIcon: String {
        switch report.reportType {
        case .behaviorAnalysis:
            return "chart.line.uptrend.xyaxis"
        case .growthReport:
            return "heart.text.square"
        }
    }
    
    var reportDescription: String {
        switch report.reportType {
        case .behaviorAnalysis:
            return "基于积分记录数据，运用行为分析学理论生成的专业分析报告"
        case .growthReport:
            return "基于成长日记内容，运用儿童心理学理论生成的专业成长报告"
        }
    }
}

extension MarkdownText {
    
    /**
     * 暴露解析方法用于测试
     */
    func parseMarkdownContent() -> [MarkdownElement] {
        let lines = content.components(separatedBy: .newlines)
        var elements: [MarkdownElement] = []
        var currentParagraph: [String] = []

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)

            if trimmedLine.isEmpty {
                if !currentParagraph.isEmpty {
                    elements.append(MarkdownElement(
                        id: UUID(),
                        type: .paragraph,
                        content: currentParagraph.joined(separator: " ")
                    ))
                    currentParagraph.removeAll()
                }
            } else if trimmedLine.hasPrefix("# ") {
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .heading1,
                    content: String(trimmedLine.dropFirst(2))
                ))
            } else if trimmedLine.hasPrefix("## ") {
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .heading2,
                    content: String(trimmedLine.dropFirst(3))
                ))
            } else if trimmedLine.hasPrefix("### ") {
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .heading3,
                    content: String(trimmedLine.dropFirst(4))
                ))
            } else if trimmedLine.hasPrefix("- ") || trimmedLine.hasPrefix("* ") {
                finalizeParagraph(&currentParagraph, &elements)
                elements.append(MarkdownElement(
                    id: UUID(),
                    type: .listItem,
                    content: String(trimmedLine.dropFirst(2))
                ))
            } else {
                currentParagraph.append(trimmedLine)
            }
        }

        finalizeParagraph(&currentParagraph, &elements)
        return elements
    }
    
    private func finalizeParagraph(_ currentParagraph: inout [String], _ elements: inout [MarkdownElement]) {
        if !currentParagraph.isEmpty {
            elements.append(MarkdownElement(
                id: UUID(),
                type: .paragraph,
                content: currentParagraph.joined(separator: " ")
            ))
            currentParagraph.removeAll()
        }
    }
}
